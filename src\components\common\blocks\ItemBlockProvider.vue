<script setup lang="ts">
import { provide, watch } from 'vue';
import { createItemBlockStore } from 'src/stores/item_block_store';
import type { ItemBlock } from 'src/types/models';

const props = defineProps<{
  blockId: number;
  itemBlock?: ItemBlock;
}>();

// Create the store for this block, passing the itemBlock data
const blockStore = createItemBlockStore(props.blockId, props.itemBlock);
const storeInstance = blockStore();

// Watch for changes in itemBlock and reinitialize options when they change
watch(
  () => props.itemBlock,
  (newItemBlock, oldItemBlock) => {
    if (!newItemBlock || !oldItemBlock) return;

    // Check if the type changed or options array changed
    const typeChanged = newItemBlock.type !== oldItemBlock.type;
    const optionsChanged =
      JSON.stringify(newItemBlock.options) !== JSON.stringify(oldItemBlock.options);

    if (typeChanged || optionsChanged) {
      console.log('🔄 [ItemBlockProvider] Detected itemBlock changes, reinitializing store:', {
        blockId: props.blockId,
        typeChanged,
        optionsChanged,
        oldType: oldItemBlock.type,
        newType: newItemBlock.type,
        oldOptionsCount: oldItemBlock.options?.length || 0,
        newOptionsCount: newItemBlock.options?.length || 0,
      });

      // Reinitialize the store with new itemBlock data
      storeInstance.reinitializeWithItemBlock(newItemBlock);
    }
  },
  { deep: true },
);

// Provide the store to child components
provide('blockStore', storeInstance);
</script>

<template>
  <slot />
</template>
